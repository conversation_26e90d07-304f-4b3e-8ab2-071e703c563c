# Improved Instagram Bot

A safer, more robust Instagram automation bot with better error handling, modern Selenium methods, and configurable settings.

## ⚠️ Important Warning

**This bot may violate Instagram's Terms of Service and could result in account suspension or ban. Use at your own risk. The authors are not responsible for any account suspensions or other consequences.**

## Features

### Improvements over the original version:

- ✅ **Modern Selenium 4** - Uses up-to-date Selenium methods
- ✅ **Automatic WebDriver Management** - No need to manually download ChromeDriver
- ✅ **Better Error Handling** - Comprehensive exception handling and logging
- ✅ **Configuration File Support** - JSON-based configuration
- ✅ **Multiple Element Selectors** - Fallback selectors for Instagram UI changes
- ✅ **Human-like Behavior** - Random delays and probabilities
- ✅ **Comprehensive Logging** - Detailed logs with timestamps
- ✅ **Statistics Tracking** - Track likes, comments, and errors
- ✅ **Safety Limits** - Built-in limits to prevent excessive activity
- ✅ **Headless Mode Support** - Run without opening browser window
- ✅ **Better Code Structure** - Object-oriented design with proper separation

## Requirements

- Python 3.7+
- Chrome browser installed
- Instagram account

## Installation

1. Clone or download this repository
2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

## Configuration

### Option 1: Using Configuration File (Recommended)

1. Copy `config_sample.json` to `config.json`
2. Edit `config.json` with your credentials and preferences:

```json
{
    "username": "your_instagram_username",
    "password": "your_instagram_password",
    "hashtags": ["travel", "photography", "food"],
    "comments": [
        "Amazing content! 🔥",
        "Love this! ❤️",
        "Great work! 👏"
    ],
    "max_posts_per_hashtag": 50,
    "min_delay": 3,
    "max_delay": 7,
    "comment_probability": 0.7,
    "like_probability": 0.9,
    "headless": false
}
```

### Option 2: Interactive Setup

If no config file is found, the bot will prompt you for configuration interactively.

## Configuration Options

| Option | Description | Default |
|--------|-------------|---------|
| `username` | Your Instagram username | Required |
| `password` | Your Instagram password | Required |
| `hashtags` | List of hashtags to target | Required |
| `comments` | List of comments to randomly choose from | Predefined list |
| `max_posts_per_hashtag` | Maximum posts to process per hashtag | 50 |
| `min_delay` | Minimum delay between actions (seconds) | 3 |
| `max_delay` | Maximum delay between actions (seconds) | 7 |
| `comment_probability` | Probability of commenting (0.0-1.0) | 0.7 |
| `like_probability` | Probability of liking (0.0-1.0) | 0.9 |
| `headless` | Run browser in headless mode | false |

## Usage

Run the bot:
```bash
python improved_instagram_bot.py
```

The bot will:
1. Ask for confirmation before starting
2. Load configuration from `config.json` or prompt interactively
3. Open Instagram and login
4. Process each hashtag in your list
5. Like and comment on posts based on your probability settings
6. Log all activities and show statistics at the end

## Safety Features

- **Rate Limiting**: Built-in delays between actions
- **Random Behavior**: Random delays and comment selection
- **Error Recovery**: Continues operation despite individual failures
- **Activity Limits**: Maximum posts per hashtag to prevent excessive activity
- **Comprehensive Logging**: All activities are logged for monitoring

## Logs

The bot creates detailed logs in `instagram_bot.log` including:
- Login attempts and results
- Posts liked and comments made
- Errors and warnings
- Statistics and performance metrics

## Troubleshooting

### Common Issues:

1. **ChromeDriver not found**: The bot automatically downloads ChromeDriver, but ensure Chrome browser is installed
2. **Login fails**: Check your username/password and ensure 2FA is disabled
3. **Elements not found**: Instagram frequently changes their UI; the bot includes multiple fallback selectors
4. **Account restrictions**: If Instagram detects automation, they may temporarily restrict your account

### Tips for Safer Usage:

- Use lower probability settings (0.3-0.5 for comments)
- Increase delays between actions
- Limit posts per hashtag (20-30 max)
- Don't run the bot continuously
- Use relevant hashtags for your niche
- Monitor the logs for any issues

## Legal and Ethical Considerations

- This bot is for educational purposes
- Respect Instagram's Terms of Service
- Don't spam or harass other users
- Use authentic, relevant comments
- Consider the impact on the Instagram community

## License

This project is licensed under the MIT License - see the LICENSE.md file for details.

## Disclaimer

The authors of this software are not responsible for any consequences resulting from the use of this bot, including but not limited to account suspensions, bans, or other penalties imposed by Instagram or other parties.
