#!/usr/bin/env python3
"""
Improved Instagram Bot
A safer, more robust Instagram automation bot with better error handling,
modern Selenium methods, and configurable settings.

WARNING: Using automation bots may violate Instagram's Terms of Service
and could result in account suspension. Use at your own risk.
"""

import os
import json
import logging
import random
import time
from typing import List, Dict, Optional
from dataclasses import dataclass
from pathlib import Path

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import (
    TimeoutException,
    NoSuchElementException,
    ElementClickInterceptedException,
    WebDriverException,
)
from webdriver_manager.chrome import ChromeDriverManager


@dataclass
class BotConfig:
    """Configuration class for the Instagram bot"""

    username: str = ""
    password: str = ""
    hashtags: List[str] = None
    comments: List[str] = None
    max_posts_per_hashtag: int = 50
    min_delay: int = 3
    max_delay: int = 7
    comment_probability: float = 0.7
    like_probability: float = 0.9
    headless: bool = False

    def __post_init__(self):
        if self.hashtags is None:
            self.hashtags = []
        if self.comments is None:
            self.comments = [
                "Amazing content! 🔥",
                "Love this! ❤️",
                "Great work! 👏",
                "Incredible! ✨",
                "So inspiring! 💫",
                "Beautiful! 😍",
                "Awesome post! 🙌",
                "This is fantastic! 🎉",
            ]


class InstagramBot:
    """Improved Instagram Bot with better error handling and safety features"""

    def __init__(self, config: BotConfig):
        self.config = config
        self.driver: Optional[webdriver.Chrome] = None
        self.wait: Optional[WebDriverWait] = None
        self.setup_logging()
        self.stats = {
            "posts_liked": 0,
            "comments_posted": 0,
            "errors": 0,
            "hashtags_processed": 0,
        }

    def setup_logging(self):
        """Setup logging configuration"""
        logging.basicConfig(
            level=logging.INFO,
            format="%(asctime)s - %(levelname)s - %(message)s",
            handlers=[
                logging.FileHandler("instagram_bot.log"),
                logging.StreamHandler(),
            ],
        )
        self.logger = logging.getLogger(__name__)

    def setup_driver(self) -> bool:
        """Setup Chrome WebDriver with options"""
        try:
            chrome_options = Options()
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option(
                "excludeSwitches", ["enable-automation"]
            )
            chrome_options.add_experimental_option("useAutomationExtension", False)

            if self.config.headless:
                chrome_options.add_argument("--headless")

            # Use WebDriverManager to automatically handle ChromeDriver
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)

            # Execute script to remove webdriver property
            self.driver.execute_script(
                "Object.defineProperty(navigator, 'webdriver', {get: () => undefined})"
            )

            self.wait = WebDriverWait(self.driver, 10)
            self.logger.info("WebDriver setup successful")
            return True

        except Exception as e:
            self.logger.error(f"Failed to setup WebDriver: {e}")
            return False

    def random_delay(self, min_seconds: int = None, max_seconds: int = None):
        """Add random delay to mimic human behavior"""
        min_delay = min_seconds or self.config.min_delay
        max_delay = max_seconds or self.config.max_delay
        delay = random.uniform(min_delay, max_delay)
        time.sleep(delay)

    def login(self) -> bool:
        """Login to Instagram with improved error handling"""
        try:
            self.logger.info("Attempting to login to Instagram")
            self.driver.get("https://www.instagram.com/accounts/login/")
            self.random_delay(3, 5)

            # Wait for and fill username
            username_input = self.wait.until(
                EC.presence_of_element_located((By.NAME, "username"))
            )
            username_input.clear()
            username_input.send_keys(self.config.username)
            self.random_delay(1, 2)

            # Fill password
            password_input = self.driver.find_element(By.NAME, "password")
            password_input.clear()
            password_input.send_keys(self.config.password)
            self.random_delay(1, 2)

            # Click login button
            login_button = self.driver.find_element(
                By.XPATH, "//button[@type='submit']"
            )
            login_button.click()

            self.random_delay(5, 8)

            # Handle potential "Save Info" or "Turn on Notifications" popups
            self._handle_popups()

            # Verify login success
            if (
                "instagram.com" in self.driver.current_url
                and "login" not in self.driver.current_url
            ):
                self.logger.info("Login successful")
                return True
            else:
                self.logger.error("Login failed - still on login page")
                return False

        except TimeoutException:
            self.logger.error("Login timeout - elements not found")
            return False
        except Exception as e:
            self.logger.error(f"Login error: {e}")
            return False

    def _handle_popups(self):
        """Handle common Instagram popups after login"""
        popup_selectors = [
            "//button[contains(text(), 'Not Now')]",
            "//button[contains(text(), 'Save Info')]",
            "//button[contains(text(), 'Turn On')]",
        ]

        for selector in popup_selectors:
            try:
                popup_button = WebDriverWait(self.driver, 3).until(
                    EC.element_to_be_clickable((By.XPATH, selector))
                )
                popup_button.click()
                self.random_delay(1, 2)
                self.logger.info(f"Handled popup: {selector}")
            except TimeoutException:
                continue
            except Exception as e:
                self.logger.warning(f"Error handling popup {selector}: {e}")
                continue

    def process_hashtag(self, hashtag: str) -> bool:
        """Process posts for a specific hashtag"""
        try:
            self.logger.info(f"Processing hashtag: #{hashtag}")
            hashtag_url = f"https://www.instagram.com/explore/tags/{hashtag.strip()}/"
            self.driver.get(hashtag_url)
            self.random_delay(3, 5)

            # Click on first post
            first_post = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "//article//a"))
            )
            first_post.click()
            self.random_delay(2, 4)

            posts_processed = 0
            max_posts = min(self.config.max_posts_per_hashtag, 200)  # Safety limit

            for i in range(max_posts):
                try:
                    if self._process_current_post():
                        posts_processed += 1

                    # Move to next post
                    if not self._go_to_next_post():
                        self.logger.info("No more posts available or reached end")
                        break

                    self.random_delay(15, 25)  # Longer delay between posts

                except Exception as e:
                    self.logger.error(f"Error processing post {i + 1}: {e}")
                    self.stats["errors"] += 1
                    continue

            self.logger.info(f"Processed {posts_processed} posts for #{hashtag}")
            self.stats["hashtags_processed"] += 1
            return True

        except Exception as e:
            self.logger.error(f"Error processing hashtag #{hashtag}: {e}")
            return False

    def _process_current_post(self) -> bool:
        """Process the currently opened post (like and comment)"""
        try:
            # Like the post
            if random.random() < self.config.like_probability:
                if self._like_post():
                    self.stats["posts_liked"] += 1

            # Comment on the post
            if random.random() < self.config.comment_probability:
                if self._comment_on_post():
                    self.stats["comments_posted"] += 1

            return True

        except Exception as e:
            self.logger.error(f"Error processing current post: {e}")
            return False

    def _like_post(self) -> bool:
        """Like the current post"""
        try:
            # Multiple selectors for like button (Instagram changes these frequently)
            like_selectors = [
                "//button[contains(@aria-label, 'Like')]",
                "//span[contains(@class, 'glyphsSpriteHeart')]//parent::button",
                "//*[name()='svg'][@aria-label='Like']//parent::button",
            ]

            for selector in like_selectors:
                try:
                    like_button = self.driver.find_element(By.XPATH, selector)
                    if (
                        like_button
                        and "liked" not in like_button.get_attribute("class").lower()
                    ):
                        like_button.click()
                        self.logger.info("Post liked successfully")
                        self.random_delay(1, 3)
                        return True
                except NoSuchElementException:
                    continue

            self.logger.warning("Could not find like button")
            return False

        except Exception as e:
            self.logger.error(f"Error liking post: {e}")
            return False

    def _comment_on_post(self) -> bool:
        """Comment on the current post"""
        try:
            # Find comment box
            comment_selectors = [
                "//textarea[@placeholder='Add a comment...']",
                "//textarea[@aria-label='Add a comment...']",
                "//form//textarea",
            ]

            comment_box = None
            for selector in comment_selectors:
                try:
                    comment_box = self.driver.find_element(By.XPATH, selector)
                    break
                except NoSuchElementException:
                    continue

            if not comment_box:
                self.logger.warning("Could not find comment box")
                return False

            # Select random comment
            comment_text = random.choice(self.config.comments)

            # Click and type comment
            comment_box.click()
            self.random_delay(1, 2)
            comment_box.send_keys(comment_text)
            self.random_delay(2, 4)
            comment_box.send_keys(Keys.ENTER)

            self.logger.info(f"Comment posted: {comment_text}")
            self.random_delay(3, 5)
            return True

        except Exception as e:
            self.logger.error(f"Error commenting on post: {e}")
            return False

    def _go_to_next_post(self) -> bool:
        """Navigate to the next post"""
        try:
            next_selectors = [
                "//button[contains(@aria-label, 'Next')]",
                "//a[contains(text(), 'Next')]",
                "//*[name()='svg'][@aria-label='Next']//parent::button",
            ]

            for selector in next_selectors:
                try:
                    next_button = self.driver.find_element(By.XPATH, selector)
                    next_button.click()
                    self.random_delay(2, 4)
                    return True
                except NoSuchElementException:
                    continue
                except ElementClickInterceptedException:
                    # Try clicking with JavaScript
                    self.driver.execute_script("arguments[0].click();", next_button)
                    self.random_delay(2, 4)
                    return True

            return False

        except Exception as e:
            self.logger.error(f"Error going to next post: {e}")
            return False

    def run(self) -> bool:
        """Main method to run the Instagram bot"""
        try:
            self.logger.info("Starting Instagram Bot")

            if not self.setup_driver():
                return False

            if not self.login():
                return False

            # Process each hashtag
            for hashtag in self.config.hashtags:
                self.logger.info(f"Starting to process hashtag: #{hashtag}")
                self.process_hashtag(hashtag)

                # Longer delay between hashtags
                if hashtag != self.config.hashtags[-1]:  # Not the last hashtag
                    self.random_delay(60, 120)  # 1-2 minutes between hashtags

            self._print_stats()
            return True

        except KeyboardInterrupt:
            self.logger.info("Bot stopped by user")
            return False
        except Exception as e:
            self.logger.error(f"Unexpected error in main run: {e}")
            return False
        finally:
            self.cleanup()

    def _print_stats(self):
        """Print bot statistics"""
        self.logger.info("=== Bot Statistics ===")
        self.logger.info(f"Hashtags processed: {self.stats['hashtags_processed']}")
        self.logger.info(f"Posts liked: {self.stats['posts_liked']}")
        self.logger.info(f"Comments posted: {self.stats['comments_posted']}")
        self.logger.info(f"Errors encountered: {self.stats['errors']}")
        self.logger.info("=====================")

    def cleanup(self):
        """Clean up resources"""
        if self.driver:
            try:
                self.driver.quit()
                self.logger.info("WebDriver closed successfully")
            except Exception as e:
                self.logger.error(f"Error closing WebDriver: {e}")


def load_config_from_file(config_path: str) -> BotConfig:
    """Load configuration from JSON file"""
    try:
        with open(config_path, "r") as f:
            config_data = json.load(f)

        return BotConfig(
            username=config_data.get("username", ""),
            password=config_data.get("password", ""),
            hashtags=config_data.get("hashtags", []),
            comments=config_data.get("comments", None),
            max_posts_per_hashtag=config_data.get("max_posts_per_hashtag", 50),
            min_delay=config_data.get("min_delay", 3),
            max_delay=config_data.get("max_delay", 7),
            comment_probability=config_data.get("comment_probability", 0.7),
            like_probability=config_data.get("like_probability", 0.9),
            headless=config_data.get("headless", False),
        )
    except FileNotFoundError:
        print(f"Config file {config_path} not found. Using interactive setup.")
        return get_config_interactive()
    except json.JSONDecodeError:
        print(f"Invalid JSON in {config_path}. Using interactive setup.")
        return get_config_interactive()


def get_config_interactive() -> BotConfig:
    """Get configuration interactively from user"""
    print("=== Instagram Bot Configuration ===")

    username = input("Enter your Instagram username: ").strip()
    password = input("Enter your Instagram password: ").strip()

    hashtags_input = input(
        "Enter hashtags separated by commas (e.g., travel,food,photography): "
    ).strip()
    hashtags = [
        tag.strip().replace("#", "") for tag in hashtags_input.split(",") if tag.strip()
    ]

    max_posts = input("Max posts per hashtag (default 50): ").strip()
    max_posts = int(max_posts) if max_posts.isdigit() else 50

    headless = input("Run in headless mode? (y/n, default n): ").strip().lower() == "y"

    return BotConfig(
        username=username,
        password=password,
        hashtags=hashtags,
        max_posts_per_hashtag=max_posts,
        headless=headless,
    )


def create_sample_config():
    """Create a sample configuration file"""
    sample_config = {
        "username": "your_instagram_username",
        "password": "your_instagram_password",
        "hashtags": ["travel", "photography", "food"],
        "comments": [
            "Amazing content! 🔥",
            "Love this! ❤️",
            "Great work! 👏",
            "Incredible! ✨",
            "So inspiring! 💫",
        ],
        "max_posts_per_hashtag": 50,
        "min_delay": 3,
        "max_delay": 7,
        "comment_probability": 0.7,
        "like_probability": 0.9,
        "headless": false,
    }

    with open("config.json", "w") as f:
        json.dump(sample_config, f, indent=4)

    print(
        "Sample config.json created. Please edit it with your credentials and preferences."
    )


def main():
    """Main entry point"""
    print("Instagram Bot - Improved Version")
    print("WARNING: This bot may violate Instagram's Terms of Service.")
    print(
        "Use at your own risk. The authors are not responsible for any account suspensions."
    )

    response = input("Do you want to continue? (y/n): ").strip().lower()
    if response != "y":
        print("Bot cancelled by user.")
        return

    # Check if config file exists
    config_path = "config.json"
    if not Path(config_path).exists():
        create_config = (
            input("No config.json found. Create sample config? (y/n): ").strip().lower()
        )
        if create_config == "y":
            create_sample_config()
            print("Please edit config.json and run the bot again.")
            return

    # Load configuration
    config = load_config_from_file(config_path)

    if not config.username or not config.password:
        print("Username and password are required!")
        return

    if not config.hashtags:
        print("At least one hashtag is required!")
        return

    # Create and run bot
    bot = InstagramBot(config)
    success = bot.run()

    if success:
        print("Bot completed successfully!")
    else:
        print("Bot encountered errors. Check the log file for details.")


if __name__ == "__main__":
    main()
